#!/usr/bin/env python3
"""
MedGemma 4B 本地推理实现
基于 Hugging Face Transformers 库
"""

import torch
from transformers import AutoProcessor, AutoModelForImageTextToText, pipeline
from PIL import Image
import requests
import time
import os
from typing import Dict, Any, List, Optional

class MedGemmaLocalInference:
    def __init__(self, model_id: str = "google/medgemma-4b-it", device: str = "auto"):
        """
        初始化 MedGemma 本地推理
        
        Args:
            model_id: 模型ID
            device: 设备选择 ("auto", "cuda", "cpu")
        """
        self.model_id = model_id
        self.device = device
        self.model = None
        self.processor = None
        self.pipeline = None
        
        print(f"🏥 初始化 MedGemma 本地推理")
        print(f"📋 模型: {model_id}")
        print(f"🖥️  设备: {device}")
        
    def load_model_direct(self):
        """直接加载模型和处理器"""
        print("\n📥 加载模型和处理器...")
        
        try:
            # 加载模型
            self.model = AutoModelForImageTextToText.from_pretrained(
                self.model_id,
                torch_dtype=torch.bfloat16,
                device_map=self.device,
                trust_remote_code=True
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(
                self.model_id,
                trust_remote_code=True
            )
            
            print("✅ 模型和处理器加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def load_pipeline(self):
        """使用 Pipeline API 加载"""
        print("\n📥 加载 Pipeline...")
        
        try:
            self.pipeline = pipeline(
                "image-text-to-text",
                model=self.model_id,
                torch_dtype=torch.bfloat16,
                device="cuda" if torch.cuda.is_available() else "cpu",
                trust_remote_code=True
            )
            
            print("✅ Pipeline 加载成功")
            return True
            
        except Exception as e:
            print(f"❌ Pipeline 加载失败: {e}")
            return False
    
    def analyze_image_direct(self, image_path: str, prompt: str, system_prompt: str = "You are an expert radiologist.") -> Dict[str, Any]:
        """使用直接模型进行图像分析"""
        if not self.model or not self.processor:
            return {"error": "模型未加载，请先调用 load_model_direct()"}
        
        try:
            # 加载图像
            if image_path.startswith('http'):
                image = Image.open(requests.get(image_path, headers={"User-Agent": "example"}, stream=True).raw)
            else:
                image = Image.open(image_path)
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": system_prompt}]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image", "image": image}
                    ]
                }
            ]
            
            # 处理输入
            start_time = time.time()
            
            inputs = self.processor.apply_chat_template(
                messages, 
                add_generation_prompt=True, 
                tokenize=True,
                return_dict=True, 
                return_tensors="pt"
            ).to(self.model.device, dtype=torch.bfloat16)
            
            input_len = inputs["input_ids"].shape[-1]
            
            # 生成回答
            with torch.inference_mode():
                generation = self.model.generate(
                    **inputs, 
                    max_new_tokens=500, 
                    do_sample=False,
                    temperature=0.1
                )
                generation = generation[0][input_len:]
            
            # 解码结果
            decoded = self.processor.decode(generation, skip_special_tokens=True)
            
            end_time = time.time()
            
            return {
                "success": True,
                "response": decoded,
                "analysis_time": end_time - start_time,
                "input_tokens": input_len,
                "output_tokens": len(generation)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def analyze_image_pipeline(self, image_path: str, prompt: str, system_prompt: str = "You are an expert radiologist.") -> Dict[str, Any]:
        """使用 Pipeline 进行图像分析"""
        if not self.pipeline:
            return {"error": "Pipeline 未加载，请先调用 load_pipeline()"}
        
        try:
            # 加载图像
            if image_path.startswith('http'):
                image = Image.open(requests.get(image_path, headers={"User-Agent": "example"}, stream=True).raw)
            else:
                image = Image.open(image_path)
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": system_prompt}]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image", "image": image}
                    ]
                }
            ]
            
            # 生成回答
            start_time = time.time()
            
            output = self.pipeline(
                text=messages, 
                max_new_tokens=500,
                temperature=0.1
            )
            
            end_time = time.time()
            
            return {
                "success": True,
                "response": output[0]["generated_text"][-1]["content"],
                "analysis_time": end_time - start_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def run_medical_analysis_demo(self):
        """运行医学分析演示"""
        print("\n🏥 MedGemma 医学影像分析演示")
        print("=" * 50)
        
        # 测试图像URL
        test_image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
        
        # 测试场景
        test_scenarios = [
            {
                "name": "基础影像描述",
                "prompt": "Describe this medical image in detail.",
                "system": "You are an expert radiologist."
            },
            {
                "name": "异常检测",
                "prompt": "Analyze this chest X-ray for any abnormalities or pathological findings.",
                "system": "You are an expert radiologist specializing in chest imaging."
            },
            {
                "name": "结构化报告",
                "prompt": "Provide a structured radiology report for this image including findings, impression, and recommendations.",
                "system": "You are an experienced radiologist writing a formal report."
            }
        ]
        
        # 尝试两种方法
        methods = [
            ("Direct Model", self.analyze_image_direct),
            ("Pipeline", self.analyze_image_pipeline)
        ]
        
        for method_name, method_func in methods:
            print(f"\n🔬 使用 {method_name} 方法:")
            
            for i, scenario in enumerate(test_scenarios, 1):
                print(f"\n--- 场景 {i}: {scenario['name']} ---")
                
                result = method_func(
                    test_image_url, 
                    scenario['prompt'], 
                    scenario['system']
                )
                
                if result.get('success'):
                    print(f"✅ 分析成功")
                    print(f"⏱️  耗时: {result['analysis_time']:.2f} 秒")
                    print(f"📝 结果: {result['response'][:200]}...")
                else:
                    print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                
                # 只测试第一个场景来节省时间
                break
            
            print("-" * 50)

def main():
    """主函数"""
    print("🚀 MedGemma 4B 本地推理测试")
    
    # 检查系统要求
    print(f"\n📋 系统信息:")
    print(f"   - Python: {torch.__version__}")
    print(f"   - PyTorch: {torch.__version__}")
    print(f"   - CUDA 可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   - GPU: {torch.cuda.get_device_name()}")
        print(f"   - GPU 内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # 初始化推理器
    inferencer = MedGemmaLocalInference()
    
    # 尝试加载模型
    print(f"\n🔄 尝试加载模型...")
    
    # 方法1: 直接加载
    if inferencer.load_model_direct():
        print("✅ 直接模型加载成功，开始测试...")
        inferencer.run_medical_analysis_demo()
    else:
        # 方法2: Pipeline 加载
        print("🔄 尝试 Pipeline 方法...")
        if inferencer.load_pipeline():
            print("✅ Pipeline 加载成功，开始测试...")
            inferencer.run_medical_analysis_demo()
        else:
            print("❌ 所有加载方法都失败了")
            print("💡 可能的解决方案:")
            print("   1. 检查网络连接")
            print("   2. 确保有足够的内存/显存")
            print("   3. 更新 transformers 库: pip install -U transformers")
            print("   4. 安装 accelerate: pip install accelerate")

if __name__ == "__main__":
    main()
