#!/usr/bin/env python3
"""
测试 hf.co/unsloth/medgemma-4b-it-GGUF:BF16 模型的图片解读能力
"""

import ollama
import os
import json
import requests
import time
from typing import Dict, Any, List
from PIL import Image

class BF16MedGemmaTester:
    def __init__(self):
        self.model_name = "hf.co/unsloth/medgemma-4b-it-GGUF:BF16"
        self.client = ollama.Client()
        
    def download_test_images(self) -> List[str]:
        """下载多种医学影像用于测试"""
        test_images = {
            "chest_xray": "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png",
            "chest_xray_2": "https://upload.wikimedia.org/wikipedia/commons/thumb/6/60/Chest_X-ray_%28side_view%29.jpg/512px-Chest_X-ray_%28side_view%29.jpg",
            "medical_scan": "https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Brain_MRI_112010_rgbca.png/512px-Brain_MRI_112010_rgbca.png"
        }
        
        os.makedirs("bf16_test_images", exist_ok=True)
        downloaded_files = []
        
        for name, url in test_images.items():
            file_path = f"bf16_test_images/{name}.png"
            
            if os.path.exists(file_path):
                print(f"✅ 图像已存在: {file_path}")
                downloaded_files.append(file_path)
                continue
                
            try:
                print(f"📥 下载 {name}...")
                headers = {"User-Agent": "Mozilla/5.0 (compatible; MedGemma-BF16-Test/1.0)"}
                response = requests.get(url, headers=headers, stream=True, timeout=30)
                response.raise_for_status()
                
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print(f"✅ 成功下载: {file_path}")
                downloaded_files.append(file_path)
                
            except Exception as e:
                print(f"❌ 下载失败 {name}: {e}")
        
        return downloaded_files
    
    def test_image_analysis(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """测试图像分析能力"""
        try:
            start_time = time.time()
            
            with open(image_path, 'rb') as image_file:
                response = self.client.generate(
                    model=self.model_name,
                    prompt=prompt,
                    images=[image_file.read()],
                    stream=False
                )
            
            end_time = time.time()
            
            return {
                "success": True,
                "image_path": image_path,
                "prompt": prompt,
                "response": response['response'],
                "analysis_time": end_time - start_time,
                "model_stats": {
                    "total_duration": response.get('total_duration', 0),
                    "load_duration": response.get('load_duration', 0),
                    "prompt_eval_count": response.get('prompt_eval_count', 0),
                    "eval_count": response.get('eval_count', 0)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path,
                "prompt": prompt
            }
    
    def get_test_scenarios(self) -> List[Dict[str, str]]:
        """获取测试场景"""
        return [
            {
                "name": "基础医学影像描述",
                "prompt": "Please describe this medical image in detail. What type of medical imaging is this and what anatomical structures can you identify?"
            },
            {
                "name": "专业放射学分析",
                "prompt": "As an expert radiologist, analyze this medical image. Identify any abnormalities, pathological findings, or areas that require attention. Provide a structured radiology report."
            },
            {
                "name": "解剖结构识别",
                "prompt": "Identify and label the anatomical structures visible in this medical image. Explain the normal anatomy and any variations you observe."
            },
            {
                "name": "临床印象和建议",
                "prompt": "Based on this medical image, what is your clinical impression? What recommendations would you make for further evaluation or follow-up?"
            },
            {
                "name": "教育性解释",
                "prompt": "Explain this medical image in a way that would be educational for medical students. What key features should they learn to identify?"
            },
            {
                "name": "图像质量评估",
                "prompt": "Evaluate the technical quality of this medical image. Comment on positioning, exposure, contrast, and any factors that might affect diagnostic interpretation."
            }
        ]
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🏥 BF16 MedGemma 图片解读能力测试")
        print("=" * 60)
        print(f"📋 测试模型: {self.model_name}")
        
        # 1. 检查模型可用性
        print("\n1. 检查模型可用性...")
        try:
            models = self.client.list()
            available_models = [model.model for model in models.models]
            if self.model_name in available_models:
                print(f"✅ 模型 {self.model_name} 已找到")
            else:
                print(f"❌ 模型未找到")
                return
        except Exception as e:
            print(f"❌ 检查模型失败: {e}")
            return
        
        # 2. 下载测试图像
        print("\n2. 准备测试图像...")
        image_paths = self.download_test_images()
        
        if not image_paths:
            print("❌ 没有可用的测试图像")
            return
        
        print(f"✅ 准备了 {len(image_paths)} 个测试图像")
        
        # 3. 获取测试场景
        scenarios = self.get_test_scenarios()
        print(f"✅ 准备了 {len(scenarios)} 个测试场景")
        
        # 4. 运行测试
        print(f"\n3. 开始图片解读测试...")
        all_results = []
        
        for i, image_path in enumerate(image_paths, 1):
            image_name = os.path.basename(image_path)
            print(f"\n🖼️  测试图像 {i}: {image_name}")
            
            image_results = {
                "image_path": image_path,
                "image_name": image_name,
                "tests": []
            }
            
            for j, scenario in enumerate(scenarios, 1):
                print(f"  📋 场景 {j}: {scenario['name']}")
                
                result = self.test_image_analysis(image_path, scenario['prompt'])
                result.update(scenario)
                image_results["tests"].append(result)
                
                if result["success"]:
                    print(f"    ✅ 完成 ({result['analysis_time']:.2f}s)")
                    print(f"    💬 回答: {result['response'][:100]}...")
                else:
                    print(f"    ❌ 失败: {result.get('error', '未知错误')}")
            
            all_results.append(image_results)
        
        # 5. 生成测试报告
        self.generate_test_report(all_results)
        
        print(f"\n🎉 BF16 MedGemma 图片解读测试完成!")
    
    def generate_test_report(self, results: List[Dict[str, Any]]):
        """生成测试报告"""
        print(f"\n4. 生成测试报告...")
        
        # 计算统计信息
        total_tests = sum(len(img_result["tests"]) for img_result in results)
        successful_tests = sum(
            sum(1 for test in img_result["tests"] if test["success"])
            for img_result in results
        )
        
        total_time = sum(
            sum(test.get("analysis_time", 0) for test in img_result["tests"] if test["success"])
            for img_result in results
        )
        
        avg_time = total_time / successful_tests if successful_tests > 0 else 0
        
        # 生成报告
        report = {
            "model_name": self.model_name,
            "test_summary": {
                "total_images": len(results),
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
                "total_analysis_time": total_time,
                "average_time_per_test": avg_time
            },
            "detailed_results": results,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 保存报告
        try:
            report_file = "bf16_medgemma_test_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"✅ 详细报告已保存到: {report_file}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
        
        # 显示统计信息
        print(f"\n📊 测试统计:")
        print(f"   - 测试图像数: {report['test_summary']['total_images']}")
        print(f"   - 总测试数: {report['test_summary']['total_tests']}")
        print(f"   - 成功测试数: {report['test_summary']['successful_tests']}")
        print(f"   - 成功率: {report['test_summary']['success_rate']:.2%}")
        print(f"   - 总耗时: {report['test_summary']['total_analysis_time']:.2f} 秒")
        print(f"   - 平均耗时: {report['test_summary']['average_time_per_test']:.2f} 秒/测试")
        
        # 显示样本结果
        self.display_sample_results(results)
    
    def display_sample_results(self, results: List[Dict[str, Any]]):
        """显示样本结果"""
        print(f"\n📝 样本分析结果:")
        print("=" * 80)
        
        for img_result in results[:2]:  # 显示前两个图像的结果
            successful_tests = [test for test in img_result["tests"] if test["success"]]
            if successful_tests:
                sample_test = successful_tests[0]  # 显示第一个成功的测试
                print(f"\n🖼️  图像: {img_result['image_name']}")
                print(f"📋 测试场景: {sample_test['name']}")
                print(f"❓ 提示词: {sample_test['prompt'][:80]}...")
                print(f"💬 分析结果:")
                print(f"   {sample_test['response'][:300]}...")
                print(f"⏱️  耗时: {sample_test['analysis_time']:.2f} 秒")
                print("-" * 80)

def main():
    """主函数"""
    tester = BF16MedGemmaTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
