#!/usr/bin/env python3
"""
简单的 MedGemma 直接测试（避免 pipeline 问题）
"""

import torch
from transformers import AutoProcessor, AutoModelForImageTextToText
from PIL import Image
import requests
import time

def simple_direct_test():
    print("🧪 MedGemma 直接模型测试")
    print("=" * 40)
    
    try:
        print("📥 加载模型和处理器...")
        
        # 加载模型
        model = AutoModelForImageTextToText.from_pretrained(
            "google/medgemma-4b-it",
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # 加载处理器
        processor = AutoProcessor.from_pretrained(
            "google/medgemma-4b-it",
            trust_remote_code=True
        )
        
        print("✅ 模型和处理器加载成功")
        
        # 下载测试图像
        print("\n📸 下载测试图像...")
        image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
        image = Image.open(requests.get(image_url, headers={"User-Agent": "test"}, stream=True).raw)
        print("✅ 图像下载成功")
        
        # 构建消息
        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are an expert radiologist."}]
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Describe this chest X-ray briefly."},
                    {"type": "image", "image": image}
                ]
            }
        ]
        
        print("\n🔍 开始分析...")
        start_time = time.time()
        
        # 处理输入
        inputs = processor.apply_chat_template(
            messages, 
            add_generation_prompt=True, 
            tokenize=True,
            return_dict=True, 
            return_tensors="pt"
        ).to(model.device, dtype=torch.bfloat16)
        
        input_len = inputs["input_ids"].shape[-1]
        
        # 生成回答
        with torch.inference_mode():
            generation = model.generate(
                **inputs, 
                max_new_tokens=200, 
                do_sample=False,
                temperature=0.1
            )
            generation = generation[0][input_len:]
        
        # 解码结果
        decoded = processor.decode(generation, skip_special_tokens=True)
        
        end_time = time.time()
        
        print("✅ 分析完成!")
        print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
        print(f"📝 分析结果:")
        print(f"   {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🏥 MedGemma 4B 本地推理测试")
    
    # 检查系统信息
    print(f"\n📋 系统信息:")
    print(f"   - PyTorch: {torch.__version__}")
    print(f"   - CUDA 可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   - GPU: {torch.cuda.get_device_name()}")
    else:
        print(f"   - 使用 CPU 推理")
    
    # 运行测试
    success = simple_direct_test()
    
    if success:
        print(f"\n🎉 测试成功! MedGemma 4B 本地推理正常工作")
        print(f"💡 您现在可以:")
        print(f"   1. 运行完整演示: python medgemma_local_inference.py")
        print(f"   2. 查看使用指南: MEDGEMMA_LOCAL_GUIDE.md")
    else:
        print(f"\n❌ 测试失败")
        print(f"💡 可能的解决方案:")
        print(f"   1. 检查网络连接")
        print(f"   2. 确保有足够内存 (推荐 8GB+)")
        print(f"   3. 登录 Hugging Face: huggingface-cli login")
        print(f"   4. 接受模型使用条款")

if __name__ == "__main__":
    main()
