#!/usr/bin/env python3
"""
快速测试 BF16 MedGemma 模型
"""

import ollama
import time
import sys
import os

def quick_bf16_test():
    """快速测试 BF16 模型"""
    model_name = "hf.co/unsloth/medgemma-4b-it-GGUF:BF16"
    
    print("🚀 BF16 MedGemma 快速测试")
    print("=" * 50)
    print(f"📋 测试模型: {model_name}")
    
    client = ollama.Client()
    
    # 1. 检查模型信息
    print("\n1. 检查模型信息...")
    try:
        result = client.show(model_name)
        print(f"✅ 模型信息获取成功")
        print(f"   架构: {getattr(result, 'architecture', 'unknown')}")
        print(f"   参数: {getattr(result, 'parameters', 'unknown')}")
        
        # 检查是否有视觉能力
        if hasattr(result, 'capabilities'):
            capabilities = result.capabilities
            has_vision = 'vision' in capabilities if capabilities else False
            print(f"   视觉能力: {'✅' if has_vision else '❌'}")
        
        if hasattr(result, 'projector'):
            print(f"   投影器: ✅ {getattr(result.projector, 'architecture', 'unknown')}")
        else:
            print(f"   投影器: ❌")
            
    except Exception as e:
        print(f"❌ 获取模型信息失败: {e}")
        return False
    
    # 2. 简单文本测试
    print("\n2. 简单文本测试...")
    try:
        start_time = time.time()
        response = client.generate(
            model=model_name,
            prompt="What is a chest X-ray? Please answer briefly.",
            stream=False,
            options={"num_predict": 50}  # 限制生成长度
        )
        end_time = time.time()
        
        print(f"✅ 文本生成成功")
        print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
        print(f"📝 回答: {response['response'][:100]}...")
        
    except Exception as e:
        print(f"❌ 文本生成失败: {e}")
        return False
    
    # 3. 图像测试
    print("\n3. 图像测试...")
    
    # 使用已存在的测试图像
    test_image_paths = [
        "test_chest_xray.png",
        "test_vision_image.png",
        "sample_images/chest_xray_sample.png",
        "bf16_test_images/chest_xray.png"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("❌ 没有找到测试图像")
        return False
    
    print(f"📸 使用测试图像: {test_image}")
    
    try:
        start_time = time.time()
        
        with open(test_image, 'rb') as image_file:
            response = client.generate(
                model=model_name,
                prompt="Describe this image briefly.",
                images=[image_file.read()],
                stream=False,
                options={"num_predict": 50}  # 限制生成长度
            )
        
        end_time = time.time()
        
        print(f"✅ 图像分析成功")
        print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
        print(f"📝 分析: {response['response'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像分析失败: {e}")
        return False

def main():
    success = quick_bf16_test()
    
    if success:
        print(f"\n🎉 BF16 MedGemma 模型测试成功!")
        print(f"✅ 该模型支持图像分析功能")
    else:
        print(f"\n❌ BF16 MedGemma 模型测试失败")
        print(f"💡 可能的原因:")
        print(f"   - 模型加载问题")
        print(f"   - 视觉组件缺失或损坏")
        print(f"   - 内存不足")
        print(f"   - Ollama 版本兼容性问题")

if __name__ == "__main__":
    main()
