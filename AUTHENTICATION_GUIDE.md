# MedGemma 认证和访问指南

## 🔐 为什么需要认证？

MedGemma 是一个受限制的模型，需要：
1. **Hugging Face 账户**
2. **接受使用条款**
3. **有效的访问令牌**

## 📝 步骤 1: 创建 Hugging Face 账户

1. 访问 [https://huggingface.co/join](https://huggingface.co/join)
2. 注册新账户或登录现有账户
3. 验证邮箱地址

## 🔑 步骤 2: 获取访问令牌

1. 登录后访问 [https://huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. 点击 "New token"
3. 选择 "Read" 权限
4. 复制生成的令牌（格式：`hf_xxxxxxxxxx`）

## ✅ 步骤 3: 接受模型使用条款

1. 访问 [https://huggingface.co/google/medgemma-4b-it](https://huggingface.co/google/medgemma-4b-it)
2. 点击 "Access repository"
3. 阅读 Health AI Developer Foundation 条款
4. 点击 "Accept" 接受条款

## 🖥️ 步骤 4: 本地认证

### 方法 1: 使用 CLI (推荐)

```bash
# 安装 Hugging Face CLI
pip install huggingface_hub[cli]

# 登录
huggingface-cli login

# 输入您的令牌
```

### 方法 2: 环境变量

```bash
# 设置环境变量
export HUGGINGFACE_HUB_TOKEN="hf_your_token_here"

# 或在 Python 中
import os
os.environ["HUGGINGFACE_HUB_TOKEN"] = "hf_your_token_here"
```

### 方法 3: 代码中直接使用

```python
from huggingface_hub import login

# 使用令牌登录
login(token="hf_your_token_here")
```

## 🧪 验证认证

运行以下命令验证认证是否成功：

```bash
huggingface-cli whoami
```

应该显示您的用户名和邮箱。

## 🚀 测试访问

认证成功后，运行：

```bash
python simple_direct_test.py
```

## ❌ 常见问题

### 问题 1: "401 Client Error: Unauthorized"
**解决方案**:
- 检查令牌是否正确
- 确认已接受模型使用条款
- 重新登录: `huggingface-cli logout` 然后 `huggingface-cli login`

### 问题 2: "Access to model is restricted"
**解决方案**:
- 访问模型页面确认已接受条款
- 等待几分钟让权限生效
- 检查账户是否已验证邮箱

### 问题 3: "Cannot access gated repo"
**解决方案**:
- 确认模型名称正确: `google/medgemma-4b-it`
- 检查网络连接
- 尝试清除缓存: `huggingface-cli cache clear`

## 🔒 安全提醒

- **不要分享您的令牌**
- **不要在公共代码中硬编码令牌**
- **定期轮换令牌**
- **使用最小权限原则**

## 📞 获取帮助

如果仍有问题：
1. 检查 [Hugging Face 文档](https://huggingface.co/docs)
2. 访问 [MedGemma 官方页面](https://developers.google.com/health-ai-developer-foundations/medgemma)
3. 查看 [GitHub Issues](https://github.com/google-health/medgemma/issues)
