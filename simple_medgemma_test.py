#!/usr/bin/env python3
"""
简单的 MedGemma 测试脚本
"""

import torch
from transformers import pipeline
from PIL import Image
import requests

def simple_test():
    print("🧪 MedGemma 简单测试")
    
    try:
        # 创建 pipeline
        pipe = pipeline(
            "image-text-to-text",
            model="google/medgemma-4b-it",
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        # 测试图像
        image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
        image = Image.open(requests.get(image_url, headers={"User-Agent": "test"}, stream=True).raw)
        
        # 分析
        messages = [{
            "role": "user",
            "content": [
                {"type": "text", "text": "Describe this chest X-ray briefly."},
                {"type": "image", "image": image}
            ]
        }]
        
        result = pipe(text=messages, max_new_tokens=100)
        print(f"✅ 测试成功!")
        print(f"📝 结果: {result[0]['generated_text'][-1]['content']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    simple_test()
