#!/usr/bin/env python3
"""
医学影像专项测试脚本
支持多种医学影像类型的分析测试
"""

import ollama
import os
import json
import requests
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Dict, Any
import time

class MedicalImageAnalyzer:
    def __init__(self, model_name: str = "medgemma:4b"):
        """初始化医学影像分析器"""
        self.model_name = model_name
        self.client = ollama.Client()
        self.supported_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        
    def get_medical_image_samples(self) -> Dict[str, str]:
        """获取医学影像样本 URL"""
        return {
            "chest_xray": "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png",
            "knee_xray": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/8b/Knee_X-ray_lateral_view.jpg/512px-Knee_X-ray_lateral_view.jpg",
            "skull_xray": "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5c/Skull_X-ray_lateral_view.jpg/512px-Skull_X-ray_lateral_view.jpg"
        }
    
    def download_medical_samples(self, save_dir: str = "medical_samples") -> List[str]:
        """下载医学影像样本"""
        os.makedirs(save_dir, exist_ok=True)
        downloaded_files = []
        
        samples = self.get_medical_image_samples()
        
        for name, url in samples.items():
            file_path = os.path.join(save_dir, f"{name}.png")
            
            if os.path.exists(file_path):
                print(f"✅ 文件已存在: {file_path}")
                downloaded_files.append(file_path)
                continue
            
            try:
                print(f"📥 下载 {name}...")
                headers = {"User-Agent": "Mozilla/5.0 (compatible; MedGemma-Test/1.0)"}
                response = requests.get(url, headers=headers, stream=True, timeout=30)
                response.raise_for_status()
                
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print(f"✅ 成功下载: {file_path}")
                downloaded_files.append(file_path)
                
            except Exception as e:
                print(f"❌ 下载失败 {name}: {e}")
        
        return downloaded_files
    
    def create_medical_prompts(self) -> Dict[str, List[str]]:
        """创建医学影像分析提示词"""
        return {
            "basic_analysis": [
                "Describe this medical image in detail.",
                "What type of medical imaging is this?",
                "Identify the anatomical structures visible in this image."
            ],
            "clinical_assessment": [
                "Analyze this medical image for any abnormalities or pathological findings.",
                "Provide a structured radiology report for this image.",
                "What are the key clinical findings in this medical image?"
            ],
            "differential_diagnosis": [
                "Based on this medical image, what are the possible differential diagnoses?",
                "What additional imaging or tests would you recommend based on these findings?",
                "Describe any areas of concern or abnormality in this image."
            ],
            "educational": [
                "Explain the normal anatomy visible in this medical image.",
                "What imaging technique was likely used to create this image?",
                "Describe the image quality and any technical factors affecting interpretation."
            ]
        }
    
    def analyze_image_with_prompt(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """使用指定提示词分析图像"""
        try:
            start_time = time.time()
            
            with open(image_path, 'rb') as image_file:
                response = self.client.generate(
                    model=self.model_name,
                    prompt=prompt,
                    images=[image_file.read()],
                    stream=False
                )
            
            end_time = time.time()
            
            return {
                "success": True,
                "image_path": image_path,
                "prompt": prompt,
                "response": response['response'],
                "analysis_time": end_time - start_time,
                "model_stats": {
                    "total_duration": response.get('total_duration', 0),
                    "load_duration": response.get('load_duration', 0),
                    "prompt_eval_count": response.get('prompt_eval_count', 0),
                    "eval_count": response.get('eval_count', 0)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path,
                "prompt": prompt
            }
    
    def run_comprehensive_analysis(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """运行综合医学影像分析"""
        all_results = []
        prompts = self.create_medical_prompts()
        
        for image_path in image_paths:
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                continue
            
            print(f"\n🔍 分析图像: {os.path.basename(image_path)}")
            image_results = {"image_path": image_path, "analyses": []}
            
            for category, prompt_list in prompts.items():
                print(f"  📋 {category} 分析...")
                
                for i, prompt in enumerate(prompt_list):
                    print(f"    {i+1}. 执行分析...")
                    
                    result = self.analyze_image_with_prompt(image_path, prompt)
                    result["category"] = category
                    result["prompt_index"] = i
                    
                    image_results["analyses"].append(result)
                    
                    if result["success"]:
                        print(f"    ✅ 完成 ({result['analysis_time']:.2f}s)")
                    else:
                        print(f"    ❌ 失败: {result.get('error', '未知错误')}")
            
            all_results.append(image_results)
        
        return all_results
    
    def generate_analysis_report(self, results: List[Dict[str, Any]], output_file: str = "medical_analysis_report.json"):
        """生成分析报告"""
        try:
            # 计算统计信息
            total_analyses = sum(len(img_result["analyses"]) for img_result in results)
            successful_analyses = sum(
                sum(1 for analysis in img_result["analyses"] if analysis["success"])
                for img_result in results
            )
            
            total_time = sum(
                sum(analysis.get("analysis_time", 0) for analysis in img_result["analyses"] if analysis["success"])
                for img_result in results
            )
            
            report = {
                "summary": {
                    "total_images": len(results),
                    "total_analyses": total_analyses,
                    "successful_analyses": successful_analyses,
                    "success_rate": successful_analyses / total_analyses if total_analyses > 0 else 0,
                    "total_analysis_time": total_time,
                    "average_time_per_analysis": total_time / successful_analyses if successful_analyses > 0 else 0
                },
                "detailed_results": results,
                "model_info": {
                    "model_name": self.model_name,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n📊 分析报告:")
            print(f"   - 总图像数: {report['summary']['total_images']}")
            print(f"   - 总分析数: {report['summary']['total_analyses']}")
            print(f"   - 成功率: {report['summary']['success_rate']:.2%}")
            print(f"   - 总耗时: {report['summary']['total_analysis_time']:.2f} 秒")
            print(f"   - 平均耗时: {report['summary']['average_time_per_analysis']:.2f} 秒/分析")
            print(f"✅ 详细报告已保存到: {output_file}")
            
            return report
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            return None
    
    def display_sample_results(self, results: List[Dict[str, Any]], max_samples: int = 3):
        """显示样本分析结果"""
        print(f"\n📝 样本分析结果 (显示前 {max_samples} 个):")
        print("=" * 80)
        
        for i, img_result in enumerate(results[:max_samples]):
            image_name = os.path.basename(img_result["image_path"])
            print(f"\n🖼️  图像: {image_name}")
            
            successful_analyses = [a for a in img_result["analyses"] if a["success"]]
            if successful_analyses:
                # 显示第一个成功的分析结果
                sample_analysis = successful_analyses[0]
                print(f"📋 分析类别: {sample_analysis['category']}")
                print(f"❓ 提示词: {sample_analysis['prompt'][:100]}...")
                print(f"💬 分析结果: {sample_analysis['response'][:300]}...")
                print(f"⏱️  耗时: {sample_analysis['analysis_time']:.2f} 秒")
            else:
                print("❌ 该图像没有成功的分析结果")

def main():
    """主函数"""
    print("🏥 医学影像专项分析测试")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = MedicalImageAnalyzer()
    
    # 检查模型可用性
    try:
        models = analyzer.client.list()
        try:
            available_models = [model.model for model in models.models]
        except AttributeError:
            # 尝试其他可能的格式
            try:
                available_models = [model['model'] for model in models['models']]
            except (KeyError, TypeError):
                available_models = [str(model) for model in models.models] if hasattr(models, 'models') else []

        if analyzer.model_name not in available_models:
            print(f"❌ 模型 {analyzer.model_name} 未找到")
            print(f"可用模型: {available_models}")
            return
        print(f"✅ 模型 {analyzer.model_name} 已准备就绪")
    except Exception as e:
        print(f"❌ 连接 Ollama 失败: {e}")
        return
    
    # 下载医学影像样本
    print(f"\n📥 下载医学影像样本...")
    image_paths = analyzer.download_medical_samples()
    
    if not image_paths:
        print("❌ 没有可用的医学影像样本")
        return
    
    print(f"✅ 成功准备 {len(image_paths)} 个医学影像样本")
    
    # 运行综合分析
    print(f"\n🔬 开始综合医学影像分析...")
    results = analyzer.run_comprehensive_analysis(image_paths)
    
    # 生成报告
    print(f"\n📊 生成分析报告...")
    report = analyzer.generate_analysis_report(results)
    
    # 显示样本结果
    analyzer.display_sample_results(results)
    
    print(f"\n🎉 医学影像分析测试完成!")

if __name__ == "__main__":
    main()
