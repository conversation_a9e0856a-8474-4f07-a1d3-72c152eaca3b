#!/usr/bin/env python3
"""
MedGemma 4B 医学影像图谱测试脚本
使用 Ollama API 调用本地 medgemma:4b 模型
"""

import ollama
import base64
import json
import requests
from PIL import Image
import io
import os
import sys
from typing import Optional, Dict, Any

class MedGemmaOllamaTester:
    def __init__(self, model_name: str = "medgemma:4b"):
        """
        初始化 MedGemma Ollama 测试器
        
        Args:
            model_name: Ollama 中的模型名称
        """
        self.model_name = model_name
        self.client = ollama.Client()
        
    def check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            models = self.client.list()
            try:
                available_models = [model.model for model in models.models]
            except AttributeError:
                # 尝试其他可能的格式
                try:
                    available_models = [model['model'] for model in models['models']]
                except (KeyError, TypeError):
                    available_models = [str(model) for model in models.models] if hasattr(models, 'models') else []

            if self.model_name in available_models:
                print(f"✅ 模型 {self.model_name} 已找到")
                return True
            else:
                print(f"❌ 模型 {self.model_name} 未找到")
                print(f"可用模型: {available_models}")
                return False
        except Exception as e:
            print(f"❌ 检查模型时出错: {e}")
            return False
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图像编码为 base64 字符串"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ 编码图像时出错: {e}")
            return ""
    
    def download_sample_image(self, url: str, save_path: str) -> bool:
        """下载示例医学影像"""
        try:
            headers = {"User-Agent": "Mozilla/5.0 (compatible; MedGemma-Test/1.0)"}
            response = requests.get(url, headers=headers, stream=True)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 成功下载图像到: {save_path}")
            return True
        except Exception as e:
            print(f"❌ 下载图像失败: {e}")
            return False
    
    def analyze_medical_image(self, image_path: str, prompt: str = None) -> Dict[str, Any]:
        """
        分析医学影像
        
        Args:
            image_path: 图像文件路径
            prompt: 自定义提示词，如果为 None 则使用默认提示
            
        Returns:
            包含分析结果的字典
        """
        if not os.path.exists(image_path):
            return {"error": f"图像文件不存在: {image_path}"}
        
        # 默认提示词
        if prompt is None:
            prompt = "You are an expert radiologist. Please analyze this medical image and provide a detailed description of your findings, including any abnormalities, anatomical structures visible, and potential clinical significance."
        
        try:
            # 使用 Ollama 的 generate 方法处理图像
            with open(image_path, 'rb') as image_file:
                response = self.client.generate(
                    model=self.model_name,
                    prompt=prompt,
                    images=[image_file.read()],
                    stream=False
                )
            
            return {
                "success": True,
                "image_path": image_path,
                "prompt": prompt,
                "response": response['response'],
                "model": response.get('model', self.model_name),
                "total_duration": response.get('total_duration', 0),
                "load_duration": response.get('load_duration', 0),
                "prompt_eval_count": response.get('prompt_eval_count', 0),
                "eval_count": response.get('eval_count', 0)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path,
                "prompt": prompt
            }
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🏥 MedGemma 4B 医学影像分析测试")
        print("=" * 50)
        
        # 1. 检查模型可用性
        print("\n1. 检查模型可用性...")
        if not self.check_model_availability():
            print("请确保已安装并拉取 medgemma:4b 模型")
            print("运行: ollama pull medgemma:4b")
            return
        
        # 2. 创建示例图像目录
        os.makedirs("sample_images", exist_ok=True)
        
        # 3. 下载示例胸部 X 光图像
        print("\n2. 下载示例医学影像...")
        chest_xray_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
        chest_xray_path = "sample_images/chest_xray_sample.png"
        
        if not os.path.exists(chest_xray_path):
            self.download_sample_image(chest_xray_url, chest_xray_path)
        
        # 4. 测试不同的医学影像分析场景
        test_scenarios = [
            {
                "name": "胸部 X 光基础分析",
                "image": chest_xray_path,
                "prompt": "You are an expert radiologist. Describe this chest X-ray image in detail."
            },
            {
                "name": "胸部 X 光异常检测",
                "image": chest_xray_path,
                "prompt": "Analyze this chest X-ray for any abnormalities, pathological findings, or areas of concern. Provide a structured radiology report."
            },
            {
                "name": "解剖结构识别",
                "image": chest_xray_path,
                "prompt": "Identify and describe the anatomical structures visible in this chest X-ray, including heart, lungs, ribs, and other relevant structures."
            },
            {
                "name": "临床建议",
                "image": chest_xray_path,
                "prompt": "Based on this chest X-ray, what would be your clinical impression and recommendations for further evaluation or follow-up?"
            }
        ]
        
        print(f"\n3. 运行 {len(test_scenarios)} 个测试场景...")
        
        results = []
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n--- 测试 {i}: {scenario['name']} ---")
            
            if not os.path.exists(scenario['image']):
                print(f"❌ 图像文件不存在: {scenario['image']}")
                continue
            
            result = self.analyze_medical_image(scenario['image'], scenario['prompt'])
            results.append({**scenario, **result})
            
            if result.get('success'):
                print(f"✅ 分析完成")
                print(f"📊 统计信息:")
                print(f"   - 总耗时: {result.get('total_duration', 0) / 1e9:.2f} 秒")
                print(f"   - 提示词评估: {result.get('prompt_eval_count', 0)} tokens")
                print(f"   - 生成: {result.get('eval_count', 0)} tokens")
                print(f"📝 分析结果:")
                print(f"   {result['response'][:200]}...")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
        
        # 5. 保存测试结果
        print(f"\n4. 保存测试结果...")
        results_file = "medgemma_test_results.json"
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 测试结果已保存到: {results_file}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
        
        print(f"\n🎉 测试完成! 共运行了 {len([r for r in results if r.get('success')])} 个成功的测试")

def main():
    """主函数"""
    print("MedGemma 4B Ollama 测试工具")
    
    # 检查命令行参数
    model_name = "medgemma:4b"
    if len(sys.argv) > 1:
        model_name = sys.argv[1]
    
    # 创建测试器并运行测试
    tester = MedGemmaOllamaTester(model_name)
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
