#!/usr/bin/env python3
"""
完成 MedGemma 访问设置指导脚本
"""

import os
import time
from huggingface_hub import login, HfA<PERSON>

def check_current_status():
    """检查当前访问状态"""
    print("🔍 检查当前访问状态...")
    
    # 设置令牌
    token = "*************************************"
    os.environ["HUGGINGFACE_HUB_TOKEN"] = token
    
    try:
        # 认证
        login(token=token)
        print("✅ 令牌认证成功")
        
        # 检查模型访问
        api = HfApi()
        try:
            model_info = api.model_info("google/medgemma-4b-it")
            print("✅ 模型访问成功 - 您已有权限!")
            return True
        except Exception as e:
            if "403" in str(e) or "not in the authorized list" in str(e):
                print("❌ 模型访问被拒绝 - 需要接受使用条款")
                return False
            else:
                print(f"❌ 其他错误: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 认证失败: {e}")
        return False

def provide_access_instructions():
    """提供访问说明"""
    print("\n📋 完成模型访问的步骤:")
    print("=" * 50)
    
    print("\n1️⃣ 访问模型页面:")
    print("   🌐 https://huggingface.co/google/medgemma-4b-it")
    print("   (页面应该已经在浏览器中打开)")
    
    print("\n2️⃣ 登录您的 Hugging Face 账户:")
    print("   • 如果未登录，点击右上角 'Log In'")
    print("   • 使用您的账户凭据登录")
    
    print("\n3️⃣ 请求访问权限:")
    print("   • 在模型页面上找到 'Access repository' 按钮")
    print("   • 点击该按钮")
    print("   • 阅读 Health AI Developer Foundation 使用条款")
    print("   • 勾选同意条款的复选框")
    print("   • 点击 'Accept' 或 'Submit' 按钮")
    
    print("\n4️⃣ 等待权限生效:")
    print("   • 权限通常会立即生效")
    print("   • 有时可能需要等待几分钟")
    
    print("\n5️⃣ 验证访问:")
    print("   • 完成上述步骤后，重新运行此脚本")
    print("   • 或运行: python complete_access_setup.py")

def wait_for_access():
    """等待用户完成访问设置"""
    print("\n⏳ 等待您完成访问设置...")
    print("完成后按 Enter 键继续，或输入 'q' 退出")
    
    while True:
        user_input = input("\n按 Enter 检查访问状态 (或 'q' 退出): ").strip().lower()
        
        if user_input == 'q':
            print("👋 退出设置")
            return False
        
        print("\n🔄 重新检查访问状态...")
        if check_current_status():
            print("\n🎉 访问设置成功!")
            return True
        else:
            print("\n❌ 仍无法访问，请确保:")
            print("   1. 已登录正确的 Hugging Face 账户")
            print("   2. 已点击 'Access repository' 按钮")
            print("   3. 已接受使用条款")
            print("   4. 等待几分钟让权限生效")

def run_final_test():
    """运行最终测试"""
    print("\n🧪 运行最终访问测试...")
    
    try:
        from transformers import AutoProcessor
        
        print("📥 尝试加载处理器...")
        processor = AutoProcessor.from_pretrained(
            "google/medgemma-4b-it",
            trust_remote_code=True
        )
        print("✅ 处理器加载成功!")
        
        print("\n🎉 MedGemma 访问设置完全成功!")
        print("\n🚀 现在您可以运行:")
        print("   python authenticated_test.py")
        print("   python medgemma_local_inference.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 最终测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🏥 MedGemma 访问设置完成指导")
    print("=" * 50)
    
    # 检查当前状态
    if check_current_status():
        print("\n🎉 您已经有访问权限!")
        if run_final_test():
            return
    
    # 提供设置说明
    provide_access_instructions()
    
    # 等待用户完成设置
    if wait_for_access():
        # 运行最终测试
        run_final_test()
    
    print("\n📚 相关文档:")
    print("   • AUTHENTICATION_GUIDE.md - 详细认证指南")
    print("   • MEDGEMMA_LOCAL_GUIDE.md - 完整使用指南")
    print("   • VERIFICATION_REPORT.md - 验证报告")

if __name__ == "__main__":
    main()
