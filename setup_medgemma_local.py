#!/usr/bin/env python3
"""
MedGemma 本地推理环境设置脚本
"""

import subprocess
import sys
import os
import torch
from typing import List, Tuple, Dict, Any

class MedGemmaSetup:
    def __init__(self):
        self.required_packages = [
            "torch>=2.0.0",
            "transformers>=4.50.0", 
            "accelerate>=0.20.0",
            "pillow>=9.0.0",
            "requests>=2.25.0",
            "huggingface_hub>=0.16.0"
        ]
        
    def check_python_version(self) -> bool:
        """检查 Python 版本"""
        print("🐍 检查 Python 版本...")
        version = sys.version_info
        
        if version.major == 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} (支持)")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor}.{version.micro} (需要 Python 3.8+)")
            return False
    
    def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        print("\n💻 检查系统资源...")
        
        resources = {
            "cpu_count": os.cpu_count(),
            "cuda_available": torch.cuda.is_available(),
            "gpu_count": 0,
            "gpu_memory": 0,
            "gpu_name": "N/A"
        }
        
        if torch.cuda.is_available():
            resources["gpu_count"] = torch.cuda.device_count()
            resources["gpu_memory"] = torch.cuda.get_device_properties(0).total_memory / 1e9
            resources["gpu_name"] = torch.cuda.get_device_name(0)
            
            print(f"✅ GPU 可用: {resources['gpu_name']}")
            print(f"   - GPU 数量: {resources['gpu_count']}")
            print(f"   - GPU 内存: {resources['gpu_memory']:.1f} GB")
            
            if resources['gpu_memory'] < 8:
                print("⚠️  警告: GPU 内存可能不足，建议至少 8GB")
        else:
            print("❌ 未检测到 CUDA GPU")
            print("💡 将使用 CPU 推理（速度较慢）")
        
        print(f"📊 CPU 核心数: {resources['cpu_count']}")
        
        return resources
    
    def install_packages(self) -> bool:
        """安装必需的包"""
        print(f"\n📦 安装必需的包...")
        
        for package in self.required_packages:
            try:
                print(f"   安装 {package}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "-U", package
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"   ✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ {package} 安装失败: {e}")
                return False
        
        return True
    
    def verify_installation(self) -> bool:
        """验证安装"""
        print(f"\n🔍 验证安装...")
        
        try:
            import transformers
            import accelerate
            from PIL import Image
            import requests
            
            print(f"✅ transformers: {transformers.__version__}")
            print(f"✅ torch: {torch.__version__}")
            print(f"✅ accelerate: {accelerate.__version__}")
            print("✅ 所有依赖包验证成功")
            
            return True
            
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
            return False
    
    def test_model_access(self) -> bool:
        """测试模型访问"""
        print(f"\n🔐 测试模型访问...")
        
        try:
            from huggingface_hub import HfApi
            
            api = HfApi()
            model_info = api.model_info("google/medgemma-4b-it")
            
            print(f"✅ 模型信息获取成功")
            print(f"   - 模型大小: ~{model_info.safetensors['total'] / 1e9:.1f} GB")
            print(f"   - 下载次数: {model_info.downloads:,}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型访问失败: {e}")
            print("💡 可能需要:")
            print("   1. 登录 Hugging Face: huggingface-cli login")
            print("   2. 接受模型使用条款")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        print(f"\n📝 创建测试脚本...")
        
        test_script = '''#!/usr/bin/env python3
"""
简单的 MedGemma 测试脚本
"""

import torch
from transformers import pipeline
from PIL import Image
import requests

def simple_test():
    print("🧪 MedGemma 简单测试")
    
    try:
        # 创建 pipeline
        pipe = pipeline(
            "image-text-to-text",
            model="google/medgemma-4b-it",
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        # 测试图像
        image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
        image = Image.open(requests.get(image_url, headers={"User-Agent": "test"}, stream=True).raw)
        
        # 分析
        messages = [{
            "role": "user",
            "content": [
                {"type": "text", "text": "Describe this chest X-ray briefly."},
                {"type": "image", "image": image}
            ]
        }]
        
        result = pipe(text=messages, max_new_tokens=100)
        print(f"✅ 测试成功!")
        print(f"📝 结果: {result[0]['generated_text'][-1]['content']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    simple_test()
'''
        
        with open("simple_medgemma_test.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        print("✅ 测试脚本已创建: simple_medgemma_test.py")
    
    def run_setup(self):
        """运行完整设置流程"""
        print("🚀 MedGemma 本地推理环境设置")
        print("=" * 50)
        
        # 1. 检查 Python 版本
        if not self.check_python_version():
            print("❌ Python 版本不兼容，请升级到 Python 3.8+")
            return False
        
        # 2. 检查系统资源
        resources = self.check_system_resources()
        
        # 3. 安装包
        if not self.install_packages():
            print("❌ 包安装失败")
            return False
        
        # 4. 验证安装
        if not self.verify_installation():
            print("❌ 安装验证失败")
            return False
        
        # 5. 测试模型访问
        model_accessible = self.test_model_access()
        
        # 6. 创建测试脚本
        self.create_test_script()
        
        # 7. 总结
        print(f"\n🎉 设置完成!")
        print(f"📋 总结:")
        print(f"   - Python: ✅")
        print(f"   - 依赖包: ✅")
        print(f"   - GPU 支持: {'✅' if resources['cuda_available'] else '❌'}")
        print(f"   - 模型访问: {'✅' if model_accessible else '❌'}")
        
        print(f"\n🚀 下一步:")
        if model_accessible:
            print(f"   1. 运行测试: python simple_medgemma_test.py")
            print(f"   2. 运行完整演示: python medgemma_local_inference.py")
        else:
            print(f"   1. 登录 Hugging Face: huggingface-cli login")
            print(f"   2. 访问并接受条款: https://huggingface.co/google/medgemma-4b-it")
            print(f"   3. 重新运行设置: python setup_medgemma_local.py")
        
        return True

def main():
    setup = MedGemmaSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
