#!/usr/bin/env python3
"""
快速测试脚本 - 简单验证 MedGemma 模型是否正常工作
"""

import ollama
import requests
import os
import sys

def quick_test(model_name: str = "medgemma:4b"):
    """快速测试 MedGemma 模型"""
    print(f"🚀 MedGemma 快速测试 - {model_name}")
    print("=" * 40)
    
    # 1. 检查 Ollama 连接
    try:
        client = ollama.Client()
        models = client.list()
        print("✅ Ollama 连接成功")
    except Exception as e:
        print(f"❌ Ollama 连接失败: {e}")
        print("请确保 Ollama 服务正在运行: ollama serve")
        return False
    
    # 2. 检查模型是否存在
    try:
        available_models = [model.model for model in models.models]
    except AttributeError:
        # 尝试其他可能的格式
        try:
            available_models = [model['model'] for model in models['models']]
        except (KeyError, TypeError):
            available_models = [str(model) for model in models.models] if hasattr(models, 'models') else []

    if model_name not in available_models:
        print(f"❌ 模型 {model_name} 未找到")
        print(f"可用模型: {available_models}")
        print(f"请运行: ollama pull {model_name}")
        return False
    
    print(f"✅ 模型 {model_name} 已找到")
    
    # 3. 文本测试
    print("\n📝 测试文本生成...")
    try:
        response = client.generate(
            model=model_name,
            prompt="What is a chest X-ray and what can it diagnose?",
            stream=False
        )
        print("✅ 文本生成测试成功")
        print(f"📄 回答: {response['response'][:150]}...")
    except Exception as e:
        print(f"❌ 文本生成测试失败: {e}")
        return False
    
    # 4. 下载测试图像
    print("\n🖼️  准备测试图像...")
    test_image_path = "test_chest_xray.png"
    
    if not os.path.exists(test_image_path):
        try:
            url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
            headers = {"User-Agent": "Mozilla/5.0 (compatible; MedGemma-Test/1.0)"}
            response = requests.get(url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(test_image_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"✅ 测试图像下载成功: {test_image_path}")
        except Exception as e:
            print(f"❌ 下载测试图像失败: {e}")
            return False
    else:
        print(f"✅ 测试图像已存在: {test_image_path}")
    
    # 5. 图像分析测试
    print("\n🔍 测试图像分析...")
    try:
        with open(test_image_path, 'rb') as image_file:
            response = client.generate(
                model=model_name,
                prompt="Describe this chest X-ray image.",
                images=[image_file.read()],
                stream=False
            )
        
        print("✅ 图像分析测试成功")
        print(f"📊 分析结果: {response['response'][:200]}...")
        print(f"⏱️  耗时: {response.get('total_duration', 0) / 1e9:.2f} 秒")
        
    except Exception as e:
        print(f"❌ 图像分析测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过! MedGemma 4B 模型工作正常")
    print("\n📚 接下来可以运行:")
    print("   python test_medgemma_ollama.py      # 基础测试")
    print("   python medical_image_test.py        # 综合测试")
    
    return True

if __name__ == "__main__":
    # 检查命令行参数
    model_name = "medgemma:4b"
    if len(sys.argv) > 1:
        model_name = sys.argv[1]

    success = quick_test(model_name)
    sys.exit(0 if success else 1)
