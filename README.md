# MedGemma 4B 医学影像图谱测试

这个项目用于测试 Ollama 中的 MedGemma 4B 模型在医学影像分析方面的能力。

## 功能特性

- 🏥 医学影像分析测试
- 📊 多种测试场景（基础分析、异常检测、解剖结构识别、临床建议）
- 📈 性能统计和报告生成
- 🖼️ 自动下载医学影像样本
- 📝 详细的分析结果记录

## 前置要求

1. **安装 Ollama**
   ```bash
   # macOS
   brew install ollama
   
   # 或从官网下载: https://ollama.ai
   ```

2. **拉取 MedGemma 4B 模型**
   ```bash
   ollama pull medgemma:4b
   ```

3. **验证模型安装**
   ```bash
   ollama list
   ```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基础测试

运行基础的 MedGemma 测试：

```bash
python test_medgemma_ollama.py
```

### 2. 综合医学影像分析

运行更详细的医学影像分析测试：

```bash
python medical_image_test.py
```

### 3. 自定义模型名称

如果您的模型名称不是 `medgemma:4b`，可以指定：

```bash
python test_medgemma_ollama.py your-model-name
```

## 测试场景

### 基础测试 (test_medgemma_ollama.py)

1. **胸部 X 光基础分析** - 基本的影像描述
2. **胸部 X 光异常检测** - 病理发现和异常检测
3. **解剖结构识别** - 识别可见的解剖结构
4. **临床建议** - 基于影像的临床印象和建议

### 综合分析 (medical_image_test.py)

1. **基础分析**
   - 详细影像描述
   - 影像类型识别
   - 解剖结构识别

2. **临床评估**
   - 异常和病理发现分析
   - 结构化放射学报告
   - 关键临床发现

3. **鉴别诊断**
   - 可能的鉴别诊断
   - 推荐的额外检查
   - 关注区域描述

4. **教育用途**
   - 正常解剖学解释
   - 影像技术说明
   - 图像质量评估

## 输出文件

- `medgemma_test_results.json` - 基础测试结果
- `medical_analysis_report.json` - 综合分析报告
- `sample_images/` - 下载的示例医学影像
- `medical_samples/` - 医学影像样本库

## 示例输出

```json
{
  "summary": {
    "total_images": 3,
    "total_analyses": 12,
    "successful_analyses": 12,
    "success_rate": 1.0,
    "total_analysis_time": 45.67,
    "average_time_per_analysis": 3.81
  },
  "model_info": {
    "model_name": "medgemma:4b",
    "timestamp": "2024-01-15 14:30:25"
  }
}
```

## 支持的医学影像类型

- 胸部 X 光片
- 膝关节 X 光片
- 头颅 X 光片
- 其他放射学影像

## 性能优化建议

1. **GPU 加速**: 确保 Ollama 使用 GPU 加速
2. **内存管理**: 大型影像可能需要更多内存
3. **批处理**: 对于大量影像，考虑批处理分析

## 故障排除

### 常见问题

1. **模型未找到**
   ```bash
   ollama pull medgemma:4b
   ```

2. **连接错误**
   ```bash
   ollama serve  # 启动 Ollama 服务
   ```

3. **内存不足**
   - 减少并发分析数量
   - 使用较小的图像尺寸

4. **网络问题**
   - 检查示例图像下载
   - 使用本地图像文件

### 调试模式

启用详细日志：

```bash
OLLAMA_DEBUG=1 python test_medgemma_ollama.py
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个测试工具。

## 许可证

本项目遵循 MIT 许可证。

## 参考资料

- [MedGemma 官方文档](https://developers.google.com/health-ai-developer-foundations/medgemma)
- [Ollama 官方网站](https://ollama.ai)
- [Hugging Face MedGemma](https://huggingface.co/collections/google/medgemma-release-680aade845f90bec6a3f60c4)

## 免责声明

⚠️ **重要提示**: 此工具仅用于研究和教育目的。MedGemma 模型的输出不应直接用于临床诊断、患者管理决策、治疗建议或任何其他直接的临床实践应用。所有来自 MedGemma 的输出都应被视为初步结果，需要独立验证、临床关联和通过既定的研究开发方法进行进一步调查。
