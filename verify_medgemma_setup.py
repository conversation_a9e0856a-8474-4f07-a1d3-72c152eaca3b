#!/usr/bin/env python3
"""
MedGemma 设置验证脚本
逐步验证所有组件是否正确配置
"""

import os
import sys
import torch
import subprocess
from typing import Dict, Any

class MedGemmaVerifier:
    def __init__(self):
        self.results = {}
        
    def print_header(self, title: str):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
    
    def print_step(self, step: str, status: str, details: str = ""):
        """打印步骤结果"""
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {step}: {status}")
        if details:
            print(f"   {details}")
    
    def verify_python_environment(self) -> bool:
        """验证 Python 环境"""
        self.print_header("Python 环境验证")
        
        # Python 版本
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            self.print_step("Python 版本", "PASS", f"Python {version.major}.{version.minor}.{version.micro}")
            python_ok = True
        else:
            self.print_step("Python 版本", "FAIL", f"需要 Python 3.8+，当前: {version.major}.{version.minor}")
            python_ok = False
        
        # PyTorch
        try:
            import torch
            self.print_step("PyTorch", "PASS", f"版本 {torch.__version__}")
            torch_ok = True
        except ImportError:
            self.print_step("PyTorch", "FAIL", "未安装")
            torch_ok = False
        
        # CUDA
        if torch_ok:
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                self.print_step("CUDA GPU", "PASS", f"{gpu_name} ({gpu_memory:.1f}GB)")
            else:
                self.print_step("CUDA GPU", "WARN", "未检测到，将使用 CPU")
        
        # Transformers
        try:
            import transformers
            self.print_step("Transformers", "PASS", f"版本 {transformers.__version__}")
            transformers_ok = True
        except ImportError:
            self.print_step("Transformers", "FAIL", "未安装")
            transformers_ok = False
        
        return python_ok and torch_ok and transformers_ok
    
    def verify_huggingface_auth(self) -> bool:
        """验证 Hugging Face 认证"""
        self.print_header("Hugging Face 认证验证")
        
        # 检查是否已登录
        try:
            result = subprocess.run(['huggingface-cli', 'whoami'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and "Not logged in" not in result.stdout:
                user_info = result.stdout.strip()
                self.print_step("CLI 认证", "PASS", f"已登录: {user_info}")
                return True
            else:
                self.print_step("CLI 认证", "FAIL", "未登录")
        except Exception as e:
            self.print_step("CLI 认证", "FAIL", f"检查失败: {e}")
        
        # 检查环境变量
        token = os.getenv("HUGGINGFACE_HUB_TOKEN")
        if token:
            if token.startswith("hf_"):
                self.print_step("环境变量令牌", "PASS", "已设置")
                return True
            else:
                self.print_step("环境变量令牌", "FAIL", "格式错误")
        else:
            self.print_step("环境变量令牌", "FAIL", "未设置")
        
        return False
    
    def verify_model_access(self) -> bool:
        """验证模型访问权限"""
        self.print_header("模型访问权限验证")
        
        try:
            from huggingface_hub import HfApi
            api = HfApi()
            
            # 尝试获取模型信息
            model_info = api.model_info("google/medgemma-4b-it")
            model_size = model_info.safetensors['total'] / 1e9
            self.print_step("模型访问", "PASS", f"可访问，大小: {model_size:.1f}GB")
            return True
            
        except Exception as e:
            error_msg = str(e)
            if "401" in error_msg or "Unauthorized" in error_msg:
                self.print_step("模型访问", "FAIL", "未授权，需要接受使用条款")
            elif "gated" in error_msg.lower():
                self.print_step("模型访问", "FAIL", "受限模型，需要申请访问权限")
            else:
                self.print_step("模型访问", "FAIL", f"访问失败: {error_msg}")
            return False
    
    def verify_dependencies(self) -> bool:
        """验证依赖包"""
        self.print_header("依赖包验证")
        
        dependencies = [
            ("PIL", "Pillow"),
            ("requests", "requests"),
            ("accelerate", "accelerate"),
        ]
        
        all_ok = True
        for module_name, package_name in dependencies:
            try:
                __import__(module_name)
                self.print_step(package_name, "PASS", "已安装")
            except ImportError:
                self.print_step(package_name, "FAIL", "未安装")
                all_ok = False
        
        return all_ok
    
    def provide_setup_instructions(self):
        """提供设置说明"""
        self.print_header("设置说明")
        
        print("📋 完成设置需要以下步骤:")
        print()
        
        print("1️⃣ 获取 Hugging Face 令牌:")
        print("   • 访问: https://huggingface.co/settings/tokens")
        print("   • 创建新令牌 (选择 'Read' 权限)")
        print("   • 复制令牌 (格式: hf_xxxxxxxxxx)")
        print()
        
        print("2️⃣ 设置认证 (选择一种方法):")
        print("   方法A - CLI 登录:")
        print("   huggingface-cli login")
        print()
        print("   方法B - 环境变量:")
        print("   export HUGGINGFACE_HUB_TOKEN='hf_your_token_here'")
        print()
        
        print("3️⃣ 接受模型使用条款:")
        print("   • 访问: https://huggingface.co/google/medgemma-4b-it")
        print("   • 点击 'Access repository'")
        print("   • 阅读并接受 Health AI Developer Foundation 条款")
        print()
        
        print("4️⃣ 重新运行验证:")
        print("   python verify_medgemma_setup.py")
        print()
    
    def run_verification(self):
        """运行完整验证"""
        print("🏥 MedGemma 4B 设置验证")
        print("此脚本将验证您的环境是否正确配置")
        
        # 验证各个组件
        env_ok = self.verify_python_environment()
        deps_ok = self.verify_dependencies()
        auth_ok = self.verify_huggingface_auth()
        access_ok = self.verify_model_access() if auth_ok else False
        
        # 总结结果
        self.print_header("验证总结")
        
        total_checks = 4
        passed_checks = sum([env_ok, deps_ok, auth_ok, access_ok])
        
        print(f"📊 验证结果: {passed_checks}/{total_checks} 项通过")
        print()
        
        if passed_checks == total_checks:
            print("🎉 所有验证通过! 您的环境已正确配置")
            print()
            print("🚀 现在可以运行:")
            print("   python authenticated_test.py")
            print("   python medgemma_local_inference.py")
            return True
        else:
            print("❌ 部分验证失败，需要完成设置")
            print()
            
            if not env_ok:
                print("🔧 环境问题: 运行 python setup_medgemma_local.py")
            if not auth_ok or not access_ok:
                print("🔐 认证问题: 查看下方设置说明")
            
            self.provide_setup_instructions()
            return False

def main():
    verifier = MedGemmaVerifier()
    success = verifier.run_verification()
    
    if not success:
        print("\n💡 需要帮助? 查看以下文档:")
        print("   • AUTHENTICATION_GUIDE.md - 认证指南")
        print("   • MEDGEMMA_LOCAL_GUIDE.md - 完整使用指南")
        print("   • IMPLEMENTATION_SUMMARY.md - 实现总结")

if __name__ == "__main__":
    main()
