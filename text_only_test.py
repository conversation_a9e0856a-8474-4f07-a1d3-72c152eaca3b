#!/usr/bin/env python3
"""
MedGemma 文本专用测试脚本
测试医学文本问答能力
"""

import ollama
import json
import time
import sys
from typing import List, Dict, Any

class MedGemmaTextTester:
    def __init__(self, model_name: str = "medgemma:4b"):
        """初始化文本测试器"""
        self.model_name = model_name
        self.client = ollama.Client()
    
    def check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            models = self.client.list()
            try:
                available_models = [model.model for model in models.models]
            except AttributeError:
                try:
                    available_models = [model['model'] for model in models['models']]
                except (KeyError, TypeError):
                    available_models = [str(model) for model in models.models] if hasattr(models, 'models') else []
            
            if self.model_name in available_models:
                print(f"✅ 模型 {self.model_name} 已找到")
                return True
            else:
                print(f"❌ 模型 {self.model_name} 未找到")
                print(f"可用模型: {available_models}")
                return False
        except Exception as e:
            print(f"❌ 检查模型时出错: {e}")
            return False
    
    def get_medical_questions(self) -> List[Dict[str, str]]:
        """获取医学问题列表"""
        return [
            {
                "category": "基础医学知识",
                "question": "What is pneumonia and what are its common symptoms?",
                "description": "肺炎基础知识"
            },
            {
                "category": "影像学",
                "question": "What are the key features to look for in a chest X-ray when evaluating for pneumonia?",
                "description": "胸部X光肺炎征象"
            },
            {
                "category": "诊断学",
                "question": "What is the difference between bacterial and viral pneumonia in terms of clinical presentation?",
                "description": "细菌性与病毒性肺炎鉴别"
            },
            {
                "category": "治疗学",
                "question": "What are the first-line antibiotics for community-acquired pneumonia in adults?",
                "description": "社区获得性肺炎一线抗生素"
            },
            {
                "category": "心血管",
                "question": "Explain the pathophysiology of myocardial infarction and its ECG changes.",
                "description": "心肌梗死病理生理和心电图变化"
            },
            {
                "category": "神经学",
                "question": "What are the clinical signs and symptoms of stroke, and how do you differentiate between ischemic and hemorrhagic stroke?",
                "description": "中风临床表现和类型鉴别"
            },
            {
                "category": "内分泌",
                "question": "Describe the diagnostic criteria for diabetes mellitus and its complications.",
                "description": "糖尿病诊断标准和并发症"
            },
            {
                "category": "急诊医学",
                "question": "What is the ABCDE approach in trauma assessment?",
                "description": "创伤评估ABCDE方法"
            },
            {
                "category": "药理学",
                "question": "Explain the mechanism of action of ACE inhibitors and their clinical uses.",
                "description": "ACE抑制剂作用机制和临床应用"
            },
            {
                "category": "预防医学",
                "question": "What are the recommended screening guidelines for breast cancer?",
                "description": "乳腺癌筛查指南"
            }
        ]
    
    def ask_question(self, question: str) -> Dict[str, Any]:
        """向模型提问"""
        try:
            start_time = time.time()
            
            response = self.client.generate(
                model=self.model_name,
                prompt=question,
                stream=False
            )
            
            end_time = time.time()
            
            return {
                "success": True,
                "question": question,
                "response": response['response'],
                "response_time": end_time - start_time,
                "model_stats": {
                    "total_duration": response.get('total_duration', 0),
                    "load_duration": response.get('load_duration', 0),
                    "prompt_eval_count": response.get('prompt_eval_count', 0),
                    "eval_count": response.get('eval_count', 0)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "question": question
            }
    
    def run_comprehensive_test(self):
        """运行综合文本测试"""
        print("🏥 MedGemma 医学文本问答测试")
        print("=" * 50)
        
        # 1. 检查模型可用性
        print("\n1. 检查模型可用性...")
        if not self.check_model_availability():
            print("请确保已安装 MedGemma 模型")
            return
        
        # 2. 获取测试问题
        questions = self.get_medical_questions()
        print(f"\n2. 准备了 {len(questions)} 个医学问题")
        
        # 3. 运行测试
        print(f"\n3. 开始问答测试...")
        results = []
        
        for i, q_data in enumerate(questions, 1):
            print(f"\n--- 问题 {i}: {q_data['description']} ---")
            print(f"📋 类别: {q_data['category']}")
            print(f"❓ 问题: {q_data['question'][:80]}...")
            
            result = self.ask_question(q_data['question'])
            result.update(q_data)
            results.append(result)
            
            if result['success']:
                print(f"✅ 回答完成 ({result['response_time']:.2f}s)")
                print(f"💬 回答: {result['response'][:150]}...")
            else:
                print(f"❌ 回答失败: {result.get('error', '未知错误')}")
        
        # 4. 生成统计报告
        self.generate_report(results)
        
        # 5. 保存结果
        self.save_results(results)
        
        print(f"\n🎉 测试完成!")
    
    def generate_report(self, results: List[Dict[str, Any]]):
        """生成测试报告"""
        successful_results = [r for r in results if r['success']]
        total_questions = len(results)
        successful_questions = len(successful_results)
        
        if successful_questions > 0:
            total_time = sum(r['response_time'] for r in successful_results)
            avg_time = total_time / successful_questions
            
            total_tokens_generated = sum(
                r['model_stats'].get('eval_count', 0) for r in successful_results
            )
            avg_tokens = total_tokens_generated / successful_questions if successful_questions > 0 else 0
        else:
            total_time = avg_time = total_tokens_generated = avg_tokens = 0
        
        print(f"\n📊 测试报告:")
        print(f"   - 总问题数: {total_questions}")
        print(f"   - 成功回答: {successful_questions}")
        print(f"   - 成功率: {successful_questions/total_questions:.2%}")
        print(f"   - 总耗时: {total_time:.2f} 秒")
        print(f"   - 平均耗时: {avg_time:.2f} 秒/问题")
        print(f"   - 总生成tokens: {total_tokens_generated}")
        print(f"   - 平均tokens: {avg_tokens:.1f} tokens/问题")
        
        # 按类别统计
        categories = {}
        for result in results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'success': 0}
            categories[category]['total'] += 1
            if result['success']:
                categories[category]['success'] += 1
        
        print(f"\n📈 分类别成功率:")
        for category, stats in categories.items():
            success_rate = stats['success'] / stats['total']
            print(f"   - {category}: {success_rate:.2%} ({stats['success']}/{stats['total']})")
    
    def save_results(self, results: List[Dict[str, Any]]):
        """保存测试结果"""
        try:
            output_file = f"medgemma_text_test_results_{self.model_name.replace(':', '_')}.json"
            
            report_data = {
                "model_name": self.model_name,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_questions": len(results),
                "successful_questions": len([r for r in results if r['success']]),
                "results": results
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 测试结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def display_sample_answers(self, results: List[Dict[str, Any]], max_samples: int = 3):
        """显示样本回答"""
        successful_results = [r for r in results if r['success']]
        
        print(f"\n📝 样本回答 (显示前 {max_samples} 个):")
        print("=" * 80)
        
        for i, result in enumerate(successful_results[:max_samples]):
            print(f"\n🔍 问题 {i+1}: {result['description']}")
            print(f"📋 类别: {result['category']}")
            print(f"❓ 问题: {result['question']}")
            print(f"💬 回答: {result['response']}")
            print(f"⏱️  耗时: {result['response_time']:.2f} 秒")
            print("-" * 80)

def main():
    """主函数"""
    print("🏥 MedGemma 医学文本问答测试工具")
    
    # 检查命令行参数
    model_name = "medgemma:4b"
    if len(sys.argv) > 1:
        model_name = sys.argv[1]
    
    print(f"使用模型: {model_name}")
    
    # 创建测试器并运行测试
    tester = MedGemmaTextTester(model_name)
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
